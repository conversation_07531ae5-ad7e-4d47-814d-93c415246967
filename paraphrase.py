import os
import requests
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
API_KEY = os.getenv('NEURAL_API_KEY')

if not API_KEY:
    raise ValueError("Environment variable NEURAL_API_KEY is not set")

# API configuration
url = 'https://api.aichronos.tech/v1/chat/completions'
model_name = 'gpt-4.1'

headers = {
    'Authorization': f'Bearer {API_KEY}',
    'Content-Type': 'application/json'
}

def read_file(file_path):
    """Read content from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        raise

def construct_prompt():
    """Construct the prompt by incorporating all required files."""
    logger.info("Reading prompt template...")
    prompt_template = read_file('prompt_paraphrasing_text.md')
    
    logger.info("Reading sample texts...")
    protocol1_content = read_file('protocol1.md')
    protocol2_content = read_file('protocol2.md')
    
    logger.info("Reading target text...")
    target_content = read_file('target_text.md')
    
    # Combine the sample texts
    sample_text = f"{protocol1_content}\n\n---\n\n{protocol2_content}"
    
    # Replace placeholders in the prompt template
    final_prompt = prompt_template.replace('<sample_text>', sample_text)
    final_prompt = final_prompt.replace('<target_text>', target_content)
    
    logger.info("Prompt constructed successfully")
    return final_prompt

def make_api_request(prompt):
    """Make API request to the specified endpoint."""
    logger.info("Making API request...")
    
    payload = {
        'model': model_name,
        'messages': [
            {'role': 'user', 'content': prompt}
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            content = data['choices'][0]['message']['content']
            logger.info("API request successful")
            return content
        else:
            logger.error(f"API Error: {response.status_code}")
            logger.error(f"Response: {response.text}")
            raise Exception(f"API request failed with status code {response.status_code}")
            
    except Exception as e:
        logger.error(f"Error making API request: {e}")
        raise

def save_response(content, output_file='response.md'):
    """Save the API response to a markdown file."""
    logger.info(f"Saving response to {output_file}...")
    
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            file.write(content)
        logger.info(f"Response saved successfully to {output_file}")
    except Exception as e:
        logger.error(f"Error saving response to file: {e}")
        raise

def main():
    """Main function to execute the paraphrasing workflow."""
    try:
        logger.info("Starting paraphrase script...")
        
        # Step 1: Construct the prompt
        prompt = construct_prompt()
        
        # Step 2: Make API request
        response_content = make_api_request(prompt)
        
        # Step 3: Save response as markdown
        save_response(response_content)
        
        logger.info("Paraphrase script completed successfully!")
        print("✅ Script completed successfully! Check 'response.md' for the results.")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        print(f"❌ Script failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
