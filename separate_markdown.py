#!/usr/bin/env python3
"""
Script to separate markdown code blocks from text content and save them to different files.
"""

import re
import os
from pathlib import Path

def separate_markdown_content(input_file_path):
    """
    Separates markdown code blocks from the rest of the content.
    
    Args:
        input_file_path (str): Path to the input markdown file
    
    Returns:
        tuple: (markdown_content, rest_content)
    """
    try:
        with open(input_file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except FileNotFoundError:
        print(f"Error: File '{input_file_path}' not found.")
        return None, None
    except Exception as e:
        print(f"Error reading file: {e}")
        return None, None
    
    # Pattern to match markdown code blocks (```markdown or ````markdown)
    # This pattern captures content between markdown code block delimiters
    markdown_pattern = r'```+markdown\n(.*?)\n```+'
    
    # Find all markdown code blocks
    markdown_matches = re.findall(markdown_pattern, content, re.DOTALL)
    
    if not markdown_matches:
        print("No markdown code blocks found in the file.")
        return "", content
    
    # Extract all markdown content
    markdown_content = '\n\n---\n\n'.join(markdown_matches)
    
    # Remove markdown code blocks from the original content to get the rest
    rest_content = re.sub(markdown_pattern, '', content, flags=re.DOTALL)
    
    # Clean up multiple consecutive newlines
    rest_content = re.sub(r'\n{3,}', '\n\n', rest_content)
    rest_content = rest_content.strip()
    
    return markdown_content, rest_content

def save_to_file(content, file_path):
    """
    Save content to a file.
    
    Args:
        content (str): Content to save
        file_path (str): Path where to save the file
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"Successfully saved content to: {file_path}")
    except Exception as e:
        print(f"Error saving file '{file_path}': {e}")

def main():
    # Input file path
    input_file = r"c:\Users\<USER>\Desktop\csv_stock_automation\NeuralAPI\Prompts\Instructions_20250708_100257.md"
    
    # Get the directory and base name for output files
    input_path = Path(input_file)
    base_dir = input_path.parent
    base_name = input_path.stem
    
    # Output file paths
    markdown_output = base_dir / f"{base_name}_markdown_only.md"
    rest_output = base_dir / f"{base_name}_text_content.md"
    
    print(f"Processing file: {input_file}")
    print(f"Output files will be:")
    print(f"  - Markdown content: {markdown_output}")
    print(f"  - Rest content: {rest_output}")
    print()
    
    # Separate the content
    markdown_content, rest_content = separate_markdown_content(input_file)
    
    if markdown_content is None:
        return
    
    # Save markdown content
    if markdown_content:
        save_to_file(markdown_content, markdown_output)
        print(f"Markdown content length: {len(markdown_content)} characters")
    else:
        print("No markdown content found to save.")
    
    # Save rest content
    if rest_content:
        save_to_file(rest_content, rest_output)
        print(f"Rest content length: {len(rest_content)} characters")
    else:
        print("No rest content found to save.")
    
    print("\nSeparation completed successfully!")

if __name__ == "__main__":
    main()
