#!/bin/bash
set -e

# Variables - change these according to your setup
SCRIPT_NAME="tele_bot"
SCRIPT_USER="david"  # or you can change to "ubuntu", "www-data", etc
WORKING_DIR="/home/<USER>/NeuralAPI"
PYTHON_PATH="$(which python3)"
SCRIPT_FILE="$WORKING_DIR/NeuralAPI.py"

# Make sure the working directory exists
mkdir -p "$WORKING_DIR"

# For demonstration: Let's assume you've pushed your script via git or SCP
# Place your Python Telegram monitor script (monitor.py) into WORKING_DIR before running this script

# Check if the main script exists
if [ ! -f "$SCRIPT_FILE" ]; then
    echo "Error: Main script '$SCRIPT_FILE' not found. Please ensure it's in the working directory."
    exit 1
fi

# Install dependencies (assuming requirements.txt exists)
if [ -f "$WORKING_DIR/requirements.txt" ]; then
    echo "Installing Python dependencies..."
    $PYTHON_PATH -m pip install --upgrade pip
    $PYTHON_PATH -m pip install -r "$WORKING_DIR/requirements.txt"
else
    echo "Warning: requirements.txt not found. Skipping dependency installation."
fi

# Set permissions
echo "Setting proper permissions for script..."
chmod +x "$SCRIPT_FILE"

# Create systemd service file
SERVICE_FILE="/etc/systemd/system/$SCRIPT_NAME.service"

echo "Creating systemd service file..."

sudo bash -c "cat > $SERVICE_FILE" << EOL
[Unit]
Description=Telegram Monitoring Script
After=network.target

[Service]
User=$SCRIPT_USER
WorkingDirectory=$WORKING_DIR
ExecStart=$PYTHON_PATH $SCRIPT_FILE
Restart=always
RestartSec=5
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=$SCRIPT_NAME

[Install]
WantedBy=multi-user.target
EOL

# Reload systemd to register the new service
echo "Reloading systemd daemon..."
sudo systemctl daemon-reload

# Enable and start service
echo "Enabling and starting $SCRIPT_NAME service..."
sudo systemctl enable "$SCRIPT_NAME"
sudo systemctl start "$SCRIPT_NAME"

echo "Done! Check service status using:"
echo "sudo systemctl status $SCRIPT_NAME"
echo "Check logs with:"
echo "sudo journalctl -u $SCRIPT_NAME -f"
